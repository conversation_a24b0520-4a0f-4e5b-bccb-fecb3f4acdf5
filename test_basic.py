"""
基础测试脚本 - 验证LangGraph框架是否正常工作
"""
import os
from graph import IntelligentAssistantGraph
from models import llm_manager
from config import ModelConfig

def test_basic_functionality():
    """测试基础功能"""
    print("🧪 开始基础功能测试...")
    
    # 测试配置加载
    print("1. 测试配置加载...")
    try:
        available_models = ModelConfig.get_available_models()
        print(f"   ✅ 可用模型: {available_models}")
        
        deepseek_config = ModelConfig.get_model_config("deepseek")
        print(f"   ✅ DeepSeek配置加载成功")
        
        kimi_config = ModelConfig.get_model_config("kimi")
        print(f"   ✅ Kimi配置加载成功")
    except Exception as e:
        print(f"   ❌ 配置加载失败: {e}")
        return False
    
    # 测试模型管理器
    print("2. 测试模型管理器...")
    try:
        # 不实际初始化模型，只测试管理器逻辑
        llm_manager.set_current_model("deepseek")
        current_model = llm_manager.get_current_model_name()
        print(f"   ✅ 当前模型设置成功: {current_model}")
        
        llm_manager.set_current_model("kimi")
        current_model = llm_manager.get_current_model_name()
        print(f"   ✅ 模型切换成功: {current_model}")
    except Exception as e:
        print(f"   ❌ 模型管理器测试失败: {e}")
        return False
    
    # 测试工作流图构建
    print("3. 测试工作流图构建...")
    try:
        assistant = IntelligentAssistantGraph()
        print(f"   ✅ 工作流图构建成功")
        print(f"   ✅ 图节点数量: {len(assistant.graph.nodes)}")
    except Exception as e:
        print(f"   ❌ 工作流图构建失败: {e}")
        return False
    
    print("🎉 基础功能测试全部通过!")
    return True

def test_mock_workflow():
    """测试模拟工作流（不调用真实API）"""
    print("\n🧪 开始模拟工作流测试...")
    
    try:
        from graph import GraphState
        
        # 创建模拟状态
        mock_state = GraphState(
            user_input="测试输入",
            model_type="deepseek",
            task_analysis="",
            response="",
            conversation_history=[],
            metadata={}
        )
        
        assistant = IntelligentAssistantGraph()
        
        # 测试各个节点函数（不调用LLM）
        print("1. 测试输入处理节点...")
        processed_state = assistant.process_input(mock_state)
        print(f"   ✅ 输入处理完成，对话历史长度: {len(processed_state['conversation_history'])}")
        
        print("2. 测试模型选择节点...")
        selected_state = assistant.select_model(processed_state)
        print(f"   ✅ 模型选择完成，选择的模型: {selected_state['model_type']}")
        
        print("3. 测试输出格式化节点...")
        formatted_state = assistant.format_output(selected_state)
        print(f"   ✅ 输出格式化完成，处理步骤: {formatted_state['metadata']['processing_steps']}")
        
        print("🎉 模拟工作流测试全部通过!")
        return True
        
    except Exception as e:
        print(f"   ❌ 模拟工作流测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 LangGraph + DeepSeek + Kimi 框架测试")
    print("="*60)
    
    # 基础功能测试
    basic_test_passed = test_basic_functionality()
    
    # 模拟工作流测试
    mock_test_passed = test_mock_workflow()
    
    print("\n" + "="*60)
    print("📊 测试结果汇总:")
    print(f"   基础功能测试: {'✅ 通过' if basic_test_passed else '❌ 失败'}")
    print(f"   模拟工作流测试: {'✅ 通过' if mock_test_passed else '❌ 失败'}")
    
    if basic_test_passed and mock_test_passed:
        print("\n🎉 所有测试通过！框架已准备就绪。")
        print("\n💡 下一步:")
        print("   1. 复制 .env 为 .env")
        print("   2. 在 .env 中填入您的API密钥")
        print("   3. 运行 python main.py 开始使用")
    else:
        print("\n❌ 部分测试失败，请检查代码。")

if __name__ == "__main__":
    main()
