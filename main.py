"""
LangGraph + DeepSeek + <PERSON>i 示例主程序
"""
import json
from typing import Dict, Any
from graph import IntelligentAssistantGraph
from models import llm_manager
from config import ModelConfig


def print_result(result: Dict[str, Any]):
    """美化打印结果"""
    print("\n" + "="*60)
    print("📊 执行结果")
    print("="*60)
    
    print(f"\n🔍 任务分析:")
    print(f"   {result.get('task_analysis', 'N/A')}")
    
    print(f"\n💬 AI响应:")
    print(f"   {result.get('response', 'N/A')}")
    
    print(f"\n📈 元数据:")
    metadata = result.get('metadata', {})
    print(f"   使用模型: {metadata.get('model_used', 'N/A')}")
    print(f"   处理步骤: {' -> '.join(metadata.get('processing_steps', []))}")
    
    print(f"\n💾 对话历史:")
    for i, msg in enumerate(result.get('conversation_history', []), 1):
        role = "👤 用户" if msg['role'] == 'user' else "🤖 助手"
        content = msg['content'][:100] + "..." if len(msg['content']) > 100 else msg['content']
        print(f"   {i}. {role}: {content}")


def interactive_demo():
    """交互式演示"""
    print("🎉 欢迎使用 LangGraph + DeepSeek + Kimi 智能助手!")
    print("="*60)
    
    # 创建工作流实例
    assistant = IntelligentAssistantGraph()
    
    # 显示可用模型
    available_models = ModelConfig.get_available_models()
    print(f"📋 可用模型: {', '.join(available_models)}")
    print("💡 提示: 在输入前加上 '@模型名' 可以切换模型，如: @kimi 你好")
    print("💡 输入 'quit' 或 'exit' 退出程序")
    print("-"*60)
    
    while True:
        try:
            # 获取用户输入
            user_input = input("\n👤 请输入您的问题: ").strip()
            
            if not user_input:
                continue
                
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见!")
                break
            
            # 检查是否指定了模型
            model_type = "deepseek"  # 默认模型
            if user_input.startswith('@'):
                parts = user_input.split(' ', 1)
                if len(parts) == 2:
                    specified_model = parts[0][1:].lower()
                    if specified_model in available_models:
                        model_type = specified_model
                        user_input = parts[1]
                    else:
                        print(f"❌ 不支持的模型: {specified_model}")
                        print(f"📋 可用模型: {', '.join(available_models)}")
                        continue
            
            # 运行工作流
            result = assistant.run(user_input, model_type)
            
            # 打印结果
            print_result(result)
            
        except KeyboardInterrupt:
            print("\n👋 程序被用户中断，再见!")
            break
        except Exception as e:
            print(f"❌ 发生错误: {str(e)}")
            print("💡 请检查您的API密钥配置是否正确")


def batch_demo():
    """批量测试演示"""
    print("🧪 批量测试演示")
    print("="*60)
    
    # 创建工作流实例
    assistant = IntelligentAssistantGraph()
    
    # 测试用例
    test_cases = [
        {
            "input": "请解释什么是人工智能",
            "model": "deepseek",
            "description": "使用DeepSeek模型回答AI问题"
        },
        {
            "input": "写一首关于春天的诗",
            "model": "kimi",
            "description": "使用Kimi模型创作诗歌"
        },
        {
            "input": "Python中如何实现单例模式？",
            "model": "deepseek",
            "description": "使用DeepSeek模型回答编程问题"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试用例 {i}: {test_case['description']}")
        print("-"*40)
        
        try:
            result = assistant.run(test_case["input"], test_case["model"])
            print_result(result)
        except Exception as e:
            print(f"❌ 测试用例 {i} 失败: {str(e)}")
        
        print("\n" + "="*60)


def main():
    """主函数"""
    print("🚀 LangGraph + DeepSeek + Kimi 示例程序")
    print("="*60)
    print("1. 交互式演示")
    print("2. 批量测试演示")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择模式 (1-3): ").strip()
        
        if choice == "1":
            interactive_demo()
            break
        elif choice == "2":
            batch_demo()
            break
        elif choice == "3":
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请输入 1、2 或 3")


if __name__ == "__main__":
    main()
