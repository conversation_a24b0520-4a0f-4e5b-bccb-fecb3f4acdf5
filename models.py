"""
LLM模型封装类
支持DeepSeek和Kimi模型
"""
from typing import Dict, Any, Optional
from langchain_openai import ChatOpenAI
from config import ModelConfig


class LLMManager:
    """LLM模型管理器"""
    
    def __init__(self):
        self._models: Dict[str, ChatOpenAI] = {}
        self._current_model: Optional[str] = None
    
    def initialize_model(self, model_type: str) -> ChatOpenAI:
        """初始化指定类型的模型"""
        if model_type not in ModelConfig.get_available_models():
            raise ValueError(f"不支持的模型类型: {model_type}")
        
        config = ModelConfig.get_model_config(model_type)
        
        # 创建ChatOpenAI实例（兼容OpenAI API格式的模型）
        model = ChatOpenAI(
            model=config["model_name"],
            openai_api_key=config["api_key"],
            openai_api_base=config["base_url"],
            temperature=config["temperature"],
            max_tokens=config["max_tokens"],
            model_kwargs={
                "top_p": config["top_p"]
            }
        )
        
        self._models[model_type] = model
        return model
    
    def get_model(self, model_type: str) -> ChatOpenAI:
        """获取指定类型的模型实例"""
        if model_type not in self._models:
            self.initialize_model(model_type)
        return self._models[model_type]
    
    def set_current_model(self, model_type: str):
        """设置当前使用的模型"""
        if model_type not in ModelConfig.get_available_models():
            raise ValueError(f"不支持的模型类型: {model_type}")
        self._current_model = model_type
    
    def get_current_model(self) -> ChatOpenAI:
        """获取当前模型实例"""
        if not self._current_model:
            self._current_model = ModelConfig.DEFAULT_MODEL
        return self.get_model(self._current_model)
    
    def get_current_model_name(self) -> str:
        """获取当前模型名称"""
        return self._current_model or ModelConfig.DEFAULT_MODEL
    
    def list_available_models(self) -> list:
        """列出可用的模型"""
        return ModelConfig.get_available_models()


# 全局模型管理器实例
llm_manager = LLMManager()
