"""
LangGraph工作流定义
创建智能助手工作流
"""
from typing import Dict, Any, List
from typing_extensions import TypedDict
from langgraph.graph import StateGraph, END
from langchain.schema import HumanMessage, AIMessage
from models import llm_manager


class GraphState(TypedDict):
    """图状态定义"""
    user_input: str
    model_type: str
    task_analysis: str
    response: str
    conversation_history: List[Dict[str, str]]
    metadata: Dict[str, Any]


class IntelligentAssistantGraph:
    """智能助手图工作流"""
    
    def __init__(self):
        self.graph = self._build_graph()
    
    def _build_graph(self) -> StateGraph:
        """构建工作流图"""
        # 创建状态图
        workflow = StateGraph(GraphState)
        
        # 添加节点
        workflow.add_node("input_processor", self.process_input)
        workflow.add_node("model_selector", self.select_model)
        workflow.add_node("task_analyzer", self.analyze_task)
        workflow.add_node("response_generator", self.generate_response)
        workflow.add_node("output_formatter", self.format_output)
        
        # 设置入口点
        workflow.set_entry_point("input_processor")
        
        # 添加边
        workflow.add_edge("input_processor", "model_selector")
        workflow.add_edge("model_selector", "task_analyzer")
        workflow.add_edge("task_analyzer", "response_generator")
        workflow.add_edge("response_generator", "output_formatter")
        workflow.add_edge("output_formatter", END)
        
        return workflow.compile()
    
    def process_input(self, state: GraphState) -> GraphState:
        """处理用户输入"""
        print(f"📝 处理用户输入: {state['user_input'][:50]}...")
        
        # 初始化对话历史
        if "conversation_history" not in state:
            state["conversation_history"] = []
        
        # 添加用户消息到历史
        state["conversation_history"].append({
            "role": "user",
            "content": state["user_input"]
        })
        
        # 初始化元数据
        state["metadata"] = {
            "timestamp": "2024-01-01",
            "processing_steps": ["input_processed"]
        }
        
        return state
    
    def select_model(self, state: GraphState) -> GraphState:
        """选择合适的模型"""
        # 如果没有指定模型，使用默认模型
        if not state.get("model_type"):
            state["model_type"] = "deepseek"
        
        print(f"🤖 选择模型: {state['model_type']}")
        
        # 设置当前模型
        llm_manager.set_current_model(state["model_type"])
        
        state["metadata"]["processing_steps"].append("model_selected")
        return state
    
    def analyze_task(self, state: GraphState) -> GraphState:
        """分析任务类型"""
        print("🔍 分析任务类型...")
        
        # 使用当前模型分析任务
        model = llm_manager.get_current_model()
        
        analysis_prompt = f"""
        请分析以下用户输入的任务类型和意图：
        用户输入: {state['user_input']}
        
        请简要说明：
        1. 任务类型（如：问答、创作、分析、编程等）
        2. 用户意图
        3. 建议的处理方式
        
        请用简洁的中文回答。
        """
        
        try:
            response = model.invoke([HumanMessage(content=analysis_prompt)])
            state["task_analysis"] = response.content
        except Exception as e:
            state["task_analysis"] = f"任务分析失败: {str(e)}"
        
        state["metadata"]["processing_steps"].append("task_analyzed")
        return state
    
    def generate_response(self, state: GraphState) -> GraphState:
        """生成响应"""
        print("💭 生成响应...")
        
        model = llm_manager.get_current_model()
        
        # 构建完整的对话上下文
        messages = []
        for msg in state["conversation_history"]:
            if msg["role"] == "user":
                messages.append(HumanMessage(content=msg["content"]))
            else:
                messages.append(AIMessage(content=msg["content"]))
        
        try:
            response = model.invoke(messages)
            state["response"] = response.content
            
            # 添加AI响应到历史
            state["conversation_history"].append({
                "role": "assistant",
                "content": response.content
            })
            
        except Exception as e:
            state["response"] = f"生成响应失败: {str(e)}"
        
        state["metadata"]["processing_steps"].append("response_generated")
        return state
    
    def format_output(self, state: GraphState) -> GraphState:
        """格式化输出"""
        print("📋 格式化输出...")
        
        # 添加格式化信息到元数据
        state["metadata"]["model_used"] = llm_manager.get_current_model_name()
        state["metadata"]["processing_steps"].append("output_formatted")
        
        return state
    
    def run(self, user_input: str, model_type: str = "deepseek") -> Dict[str, Any]:
        """运行工作流"""
        initial_state = GraphState(
            user_input=user_input,
            model_type=model_type,
            task_analysis="",
            response="",
            conversation_history=[],
            metadata={}
        )
        
        print(f"🚀 启动智能助手工作流...")
        print(f"📥 用户输入: {user_input}")
        print(f"🤖 使用模型: {model_type}")
        print("-" * 50)
        
        # 执行工作流
        result = self.graph.invoke(initial_state)
        
        print("-" * 50)
        print("✅ 工作流执行完成!")
        
        return result
