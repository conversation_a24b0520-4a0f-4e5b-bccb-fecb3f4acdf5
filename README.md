# LangGraph + DeepSeek + Kimi 智能助手

这是一个使用最新版LangGraph框架构建的智能助手示例，支持DeepSeek和Kimi模型的动态切换。

## 功能特性

- 🤖 **多模型支持**: 支持DeepSeek和Kimi模型
- 🔄 **动态切换**: 运行时可自由切换LLM模型
- 📊 **工作流管理**: 使用LangGraph构建复杂的AI工作流
- 💬 **对话历史**: 维护完整的对话上下文
- 🎯 **任务分析**: 智能分析用户意图和任务类型
- 📋 **状态管理**: 完整的状态跟踪和元数据记录

## 项目结构

```
script-engine/
├── config.py          # 模型配置文件
├── models.py           # LLM模型封装类
├── graph.py            # LangGraph工作流定义
├── main.py             # 主程序入口
├── requirements.txt    # 项目依赖
├── .env.example        # 环境变量示例
└── README.md           # 项目说明
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置API密钥

1. 复制环境变量示例文件：
```bash
cp .env .env
```

2. 编辑 `.env` 文件，填入您的API密钥：
```env
DEEPSEEK_API_KEY=your-deepseek-api-key-here
KIMI_API_KEY=your-kimi-api-key-here
```

### 获取API密钥

- **DeepSeek**: 访问 [DeepSeek官网](https://platform.deepseek.com/) 注册并获取API密钥
- **Kimi**: 访问 [Moonshot AI官网](https://platform.moonshot.cn/) 注册并获取API密钥

## 使用方法

### 运行程序

```bash
python main.py
```

### 交互式使用

程序启动后，您可以：

1. **直接提问**: 使用默认模型(DeepSeek)回答
```
👤 请输入您的问题: 什么是人工智能？
```

2. **指定模型**: 在输入前加上 `@模型名`
```
👤 请输入您的问题: @kimi 写一首关于春天的诗
👤 请输入您的问题: @deepseek Python如何实现单例模式？
```

3. **退出程序**: 输入 `quit` 或 `exit`

### 工作流说明

程序使用LangGraph构建了以下工作流：

```
用户输入 → 输入处理 → 模型选择 → 任务分析 → 响应生成 → 输出格式化
```

每个步骤都会：
- 处理和验证输入
- 选择合适的LLM模型
- 分析任务类型和用户意图
- 生成智能响应
- 格式化输出结果

## 代码示例

### 基本使用

```python
from graph import IntelligentAssistantGraph

# 创建智能助手实例
assistant = IntelligentAssistantGraph()

# 使用DeepSeek模型
result = assistant.run("解释什么是机器学习", "deepseek")

# 使用Kimi模型
result = assistant.run("写一个Python函数", "kimi")

print(result["response"])
```

### 自定义模型配置

```python
from config import ModelConfig

# 修改模型参数
ModelConfig.DEEPSEEK_CONFIG["temperature"] = 0.5
ModelConfig.KIMI_CONFIG["max_tokens"] = 4096
```

## 扩展功能

### 添加新模型

1. 在 `config.py` 中添加新模型配置
2. 在 `ModelConfig.get_available_models()` 中注册新模型
3. 确保新模型兼容OpenAI API格式

### 自定义工作流

您可以在 `graph.py` 中：
- 添加新的节点函数
- 修改工作流逻辑
- 增加条件分支
- 扩展状态定义

## 注意事项

1. **API密钥安全**: 请妥善保管您的API密钥，不要提交到版本控制系统
2. **网络连接**: 确保网络可以访问相应的API服务
3. **配额限制**: 注意各模型的API调用配额和费用
4. **错误处理**: 程序包含基本的错误处理，但建议根据实际需求完善

## 故障排除

### 常见问题

1. **API密钥错误**
   - 检查 `.env` 文件中的密钥是否正确
   - 确认密钥有效且有足够配额

2. **网络连接问题**
   - 检查网络连接
   - 确认可以访问API服务地址

3. **依赖安装问题**
   - 使用 `pip install -r requirements.txt` 重新安装依赖
   - 检查Python版本是否兼容(推荐3.8+)

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！
