# LangGraph + DeepSeek + Kimi 使用指南

## 🚀 快速开始

### 1. 环境准备
```bash
# 确保Python版本 >= 3.8
python --version

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置API密钥
```bash
# 复制环境变量模板
cp .env .env

# 编辑 .env 文件，填入真实的API密钥
DEEPSEEK_API_KEY=your-deepseek-api-key-here
KIMI_API_KEY=your-kimi-api-key-here
```

### 3. 运行测试
```bash
# 基础功能测试
python test_basic.py

# 演示模式（无需API密钥）
python demo.py
```

### 4. 正式使用
```bash
# 启动完整功能
python main.py
```

## 📋 文件说明

| 文件 | 功能 | 说明 |
|------|------|------|
| `config.py` | 配置管理 | 管理DeepSeek和Kimi的API配置 |
| `models.py` | 模型管理 | LLM模型的封装和管理 |
| `graph.py` | 工作流定义 | LangGraph工作流的核心逻辑 |
| `main.py` | 主程序 | 完整的交互式程序入口 |
| `demo.py` | 演示程序 | 模拟模式，无需真实API密钥 |
| `test_basic.py` | 测试脚本 | 基础功能测试 |

## 🎯 使用方式

### 方式1: 交互式对话
```bash
python main.py
# 选择 "1. 交互式演示"
# 直接输入问题或使用 @模型名 指定模型
```

### 方式2: 批量测试
```bash
python main.py
# 选择 "2. 批量测试演示"
# 自动运行预设的测试用例
```

### 方式3: 演示模式
```bash
python demo.py
# 无需API密钥，使用模拟响应
# 体验完整的工作流程
```

## 🔧 自定义配置

### 修改模型参数
```python
# 在 config.py 中修改
ModelConfig.DEEPSEEK_CONFIG["temperature"] = 0.5
ModelConfig.KIMI_CONFIG["max_tokens"] = 4096
```

### 添加新模型
```python
# 1. 在 config.py 中添加配置
NEW_MODEL_CONFIG = {
    "model_name": "new-model",
    "api_key": os.getenv("NEW_MODEL_API_KEY"),
    "base_url": "https://api.newmodel.com/v1",
    # ... 其他配置
}

# 2. 在 get_available_models() 中注册
@classmethod
def get_available_models(cls) -> list:
    return ["deepseek", "kimi", "new-model"]
```

### 自定义工作流
```python
# 在 graph.py 中添加新节点
def custom_node(self, state: GraphState) -> GraphState:
    # 自定义处理逻辑
    return state

# 在 _build_graph() 中添加节点和边
workflow.add_node("custom_node", self.custom_node)
workflow.add_edge("existing_node", "custom_node")
```

## 🎨 工作流详解

### 工作流步骤
1. **输入处理** (`process_input`)
   - 处理用户输入
   - 初始化对话历史
   - 设置元数据

2. **模型选择** (`select_model`)
   - 根据用户指定或默认选择模型
   - 设置当前使用的LLM

3. **任务分析** (`analyze_task`)
   - 分析用户意图和任务类型
   - 为后续处理提供指导

4. **响应生成** (`generate_response`)
   - 调用选定的LLM生成响应
   - 维护对话上下文

5. **输出格式化** (`format_output`)
   - 格式化最终输出
   - 记录处理元数据

### 状态管理
```python
class GraphState(TypedDict):
    user_input: str              # 用户输入
    model_type: str              # 使用的模型类型
    task_analysis: str           # 任务分析结果
    response: str                # AI响应
    conversation_history: List   # 对话历史
    metadata: Dict               # 元数据
```

## 🔍 API密钥获取

### DeepSeek API
1. 访问 [DeepSeek官网](https://platform.deepseek.com/)
2. 注册账号并登录
3. 在API管理页面创建新的API密钥
4. 复制密钥到 `.env` 文件

### Kimi API (Moonshot AI)
1. 访问 [Moonshot AI官网](https://platform.moonshot.cn/)
2. 注册账号并登录
3. 在控制台创建API密钥
4. 复制密钥到 `.env` 文件

## 🛠️ 故障排除

### 常见问题

**Q: 提示"不支持的模型类型"**
A: 检查模型名称是否正确，支持的模型: `deepseek`, `kimi`

**Q: API调用失败**
A: 
- 检查API密钥是否正确
- 确认网络连接正常
- 验证API配额是否充足

**Q: 依赖安装失败**
A:
- 升级pip: `python -m pip install --upgrade pip`
- 使用国内镜像: `pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/`

**Q: 工作流执行卡住**
A:
- 检查API服务是否可访问
- 增加超时设置
- 查看错误日志

### 调试模式
```python
# 在代码中添加调试信息
import logging
logging.basicConfig(level=logging.DEBUG)

# 或者使用演示模式进行测试
python demo.py
```

## 📈 性能优化

### 1. 模型选择策略
- 简单问答: 使用DeepSeek
- 创意写作: 使用Kimi
- 编程问题: 使用DeepSeek

### 2. 参数调优
```python
# 降低temperature获得更稳定的输出
config["temperature"] = 0.3

# 增加max_tokens处理长文本
config["max_tokens"] = 4096
```

### 3. 缓存机制
```python
# 可以添加响应缓存避免重复调用
from functools import lru_cache

@lru_cache(maxsize=100)
def cached_llm_call(prompt):
    return llm.invoke(prompt)
```

## 🔮 扩展功能

### 1. 添加更多模型
- 支持OpenAI GPT系列
- 集成Claude模型
- 添加本地模型支持

### 2. 增强工作流
- 添加条件分支
- 实现循环处理
- 支持并行执行

### 3. 用户界面
- Web界面 (Flask/FastAPI)
- 桌面应用 (Tkinter/PyQt)
- 命令行增强 (Rich/Click)

## 📞 技术支持

如果遇到问题，请：
1. 查看本指南的故障排除部分
2. 运行 `python test_basic.py` 检查基础功能
3. 使用 `python demo.py` 验证工作流逻辑
4. 检查API密钥和网络连接

---

🎉 **祝您使用愉快！** 这个框架为您提供了一个强大而灵活的LangGraph + 多模型LLM解决方案。
