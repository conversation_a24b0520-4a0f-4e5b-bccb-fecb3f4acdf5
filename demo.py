"""
演示脚本 - 展示LangGraph框架功能（模拟模式）
"""
import time
from typing import Dict, Any
from graph import IntelligentAssistantGraph, GraphState
from models import llm_manager
from config import ModelConfig


class MockLLM:
    """模拟LLM类，用于演示"""
    
    def __init__(self, model_name: str):
        self.model_name = model_name
    
    def invoke(self, messages):
        """模拟LLM调用"""
        # 模拟API调用延迟
        time.sleep(1)
        
        # 根据不同模型返回不同风格的响应
        if "deepseek" in self.model_name.lower():
            return MockResponse("这是DeepSeek模型的模拟响应。我擅长逻辑推理和代码分析。")
        elif "kimi" in self.model_name.lower() or "moonshot" in self.model_name.lower():
            return MockResponse("这是Kimi模型的模拟响应。我擅长创意写作和对话交流。")
        else:
            return MockResponse("这是通用模型的模拟响应。")


class MockResponse:
    """模拟响应类"""
    
    def __init__(self, content: str):
        self.content = content


class DemoAssistantGraph(IntelligentAssistantGraph):
    """演示版智能助手图（使用模拟LLM）"""
    
    def analyze_task(self, state: GraphState) -> GraphState:
        """分析任务类型（模拟版本）"""
        print("🔍 分析任务类型...")
        
        # 模拟任务分析
        user_input = state['user_input'].lower()
        
        if any(keyword in user_input for keyword in ['代码', '编程', 'python', 'java', '函数']):
            analysis = "任务类型: 编程相关\n用户意图: 寻求编程帮助\n建议处理方式: 提供代码示例和详细解释"
        elif any(keyword in user_input for keyword in ['诗', '故事', '创作', '写作']):
            analysis = "任务类型: 创意写作\n用户意图: 创作文学内容\n建议处理方式: 发挥创意，生成优美文本"
        elif any(keyword in user_input for keyword in ['什么是', '解释', '介绍']):
            analysis = "任务类型: 知识问答\n用户意图: 获取知识信息\n建议处理方式: 提供准确、详细的解释"
        else:
            analysis = "任务类型: 一般对话\n用户意图: 日常交流\n建议处理方式: 友好回应，提供有用信息"
        
        state["task_analysis"] = analysis
        state["metadata"]["processing_steps"].append("task_analyzed")
        return state
    
    def generate_response(self, state: GraphState) -> GraphState:
        """生成响应（模拟版本）"""
        print("💭 生成响应...")
        
        # 使用模拟LLM
        mock_llm = MockLLM(state["model_type"])
        
        # 根据任务分析生成不同类型的响应
        user_input = state['user_input']
        
        if "deepseek" in state["model_type"]:
            if "代码" in user_input or "编程" in user_input:
                response = f"[DeepSeek] 关于编程问题：{user_input}\n\n我建议您可以这样实现：\n```python\n# 示例代码\ndef example_function():\n    return 'Hello, World!'\n```\n\n这种方法的优点是简洁高效。"
            else:
                response = f"[DeepSeek] 针对您的问题：{user_input}\n\n经过逻辑分析，我认为这个问题可以从以下几个角度来理解：\n1. 技术层面的考虑\n2. 实际应用的场景\n3. 最佳实践的建议"
        else:  # kimi
            if "诗" in user_input or "创作" in user_input:
                response = f"[Kimi] 关于创作请求：{user_input}\n\n让我为您创作一首诗：\n\n春风轻拂柳梢头，\n绿意盎然满枝头。\n鸟儿歌唱花儿笑，\n美好时光共追求。\n\n希望这首诗能带给您愉悦的心情！"
            else:
                response = f"[Kimi] 关于您的问题：{user_input}\n\n这是一个很有趣的话题！让我用生动的方式来为您解答：\n\n想象一下，这就像是一个精彩的故事，其中包含了许多值得探索的细节和深层含义。通过这样的角度，我们可以更好地理解问题的本质。"
        
        state["response"] = response
        
        # 添加到对话历史
        state["conversation_history"].append({
            "role": "assistant",
            "content": response
        })
        
        state["metadata"]["processing_steps"].append("response_generated")
        return state


def run_demo():
    """运行演示"""
    print("🎭 LangGraph + DeepSeek + Kimi 演示模式")
    print("="*60)
    print("💡 这是演示模式，使用模拟的LLM响应")
    print("💡 要使用真实API，请配置 .env 文件中的API密钥")
    print("-"*60)
    
    # 创建演示助手
    demo_assistant = DemoAssistantGraph()
    
    # 演示用例
    demo_cases = [
        {
            "input": "请解释什么是人工智能",
            "model": "deepseek",
            "description": "使用DeepSeek模型回答AI概念问题"
        },
        {
            "input": "帮我写一首关于秋天的诗",
            "model": "kimi", 
            "description": "使用Kimi模型进行创意写作"
        },
        {
            "input": "Python中如何实现单例模式？",
            "model": "deepseek",
            "description": "使用DeepSeek模型回答编程问题"
        }
    ]
    
    for i, case in enumerate(demo_cases, 1):
        print(f"\n🎬 演示案例 {i}: {case['description']}")
        print("="*50)
        
        # 运行工作流
        result = demo_assistant.run(case["input"], case["model"])
        
        # 显示结果
        print(f"\n📋 执行结果:")
        print(f"🔍 任务分析: {result['task_analysis']}")
        print(f"\n💬 AI响应:\n{result['response']}")
        print(f"\n📊 使用模型: {result['metadata']['model_used']}")
        print(f"📈 处理步骤: {' → '.join(result['metadata']['processing_steps'])}")
        
        if i < len(demo_cases):
            print("\n" + "-"*60)
            input("按回车键继续下一个演示...")


def interactive_demo():
    """交互式演示"""
    print("\n🎮 交互式演示模式")
    print("="*60)
    print("💡 您可以输入问题，体验不同模型的响应风格")
    print("💡 输入格式: @模型名 问题内容 (如: @deepseek 什么是机器学习)")
    print("💡 可用模型: deepseek, kimi")
    print("💡 输入 'quit' 退出")
    print("-"*60)
    
    demo_assistant = DemoAssistantGraph()
    
    while True:
        try:
            user_input = input("\n👤 请输入您的问题: ").strip()
            
            if not user_input or user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 演示结束，感谢体验！")
                break
            
            # 解析模型和问题
            model_type = "deepseek"  # 默认
            if user_input.startswith('@'):
                parts = user_input.split(' ', 1)
                if len(parts) == 2:
                    specified_model = parts[0][1:].lower()
                    if specified_model in ['deepseek', 'kimi']:
                        model_type = specified_model
                        user_input = parts[1]
                    else:
                        print(f"❌ 不支持的模型: {specified_model}")
                        continue
            
            # 运行演示
            result = demo_assistant.run(user_input, model_type)
            
            # 显示结果
            print(f"\n🤖 [{result['metadata']['model_used'].upper()}] 响应:")
            print(result['response'])
            
        except KeyboardInterrupt:
            print("\n👋 演示被中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")


def main():
    """主函数"""
    print("🚀 选择演示模式:")
    print("1. 自动演示 - 运行预设案例")
    print("2. 交互演示 - 手动输入问题")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == "1":
            run_demo()
            break
        elif choice == "2":
            interactive_demo()
            break
        elif choice == "3":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请输入 1、2 或 3")


if __name__ == "__main__":
    main()
