"""
LLM模型配置文件
支持DeepSeek和Kimi模型的配置
"""
import os
from typing import Dict, Any
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class ModelConfig:
    """模型配置类"""
    
    # DeepSeek配置
    DEEPSEEK_CONFIG = {
        "model_name": "deepseek-chat",
        "api_key": os.getenv("DEEPSEEK_API_KEY", "your-deepseek-api-key"),
        "base_url": "https://api.deepseek.com/v1",
        "temperature": 0.7,
        "max_tokens": 2048,
        "top_p": 0.9,
    }
    
    # Kimi配置
    KIMI_CONFIG = {
        "model_name": "moonshot-v1-8k",
        "api_key": os.getenv("KIMI_API_KEY", "your-kimi-api-key"),
        "base_url": "https://api.moonshot.cn/v1",
        "temperature": 0.7,
        "max_tokens": 2048,
        "top_p": 0.9,
    }
    
    # 默认模型
    DEFAULT_MODEL = "deepseek"
    
    @classmethod
    def get_model_config(cls, model_type: str) -> Dict[str, Any]:
        """获取指定模型的配置"""
        if model_type.lower() == "deepseek":
            return cls.DEEPSEEK_CONFIG
        elif model_type.lower() == "kimi":
            return cls.KIMI_CONFIG
        else:
            raise ValueError(f"不支持的模型类型: {model_type}")
    
    @classmethod
    def get_available_models(cls) -> list:
        """获取可用的模型列表"""
        return ["deepseek", "kimi"]
